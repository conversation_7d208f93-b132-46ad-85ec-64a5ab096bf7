# WooCommerce Product Tags Cloud

Плагин для WordPress/WooCommerce, который генерирует облако тегов из названий товаров в категориях WooCommerce.

## Описание

Плагин автоматически извлекает первые слова из названий товаров и создает интерактивное облако тегов. Теги разделяются на числовые (например, номера моделей) и алфавитные. При клике на тег пользователь переходит к поиску товаров с этим тегом.

## Возможности

- ✅ Автоматическое извлечение тегов из названий товаров
- ✅ Разделение на числовые и алфавитные теги
- ✅ Интеграция с FiboSearch (AJAX поиск)
- ✅ Настраиваемое кэширование для производительности
- ✅ Админ-панель с настройками
- ✅ Возможность открытия ссылок в новой вкладке
- ✅ Настраиваемые стили CSS
- ✅ Поддержка шорткодов с атрибутами
- ✅ Безопасность (защита от SQL-инъекций, CSRF)
- ✅ Интернационализация (готовность к переводу)

## Установка

1. Загрузите файлы плагина в папку `/wp-content/plugins/wc-product-tags-cloud/`
2. Активируйте плагин через меню 'Плагины' в WordPress
3. Убедитесь, что WooCommerce установлен и активирован
4. Настройте плагин в разделе "Настройки" → "Product Tags Cloud"

## Использование

### Шорткод

Основной шорткод:
```
[wc_product_tags_cloud]
```

Шорткод с атрибутами:
```
[wc_product_tags_cloud show_numeric="yes" show_alpha="yes" separator=" | " class="my-tags-cloud"]
```

### Атрибуты шорткода

- `show_numeric` - показывать числовые теги (yes/no)
- `show_alpha` - показывать алфавитные теги (yes/no)
- `separator` - разделитель между тегами
- `class` - CSS класс для контейнера

### Автоматическое отображение

Плагин автоматически добавляет облако тегов:
- На страницах категорий товаров
- На страницах поиска товаров

## Настройки

### Основные настройки

- **Время кэширования** - время хранения кэша в секундах (300-604800)
- **Максимум товаров** - максимальное количество товаров для обработки (100-5000)
- **Минимальная длина тега** - минимальная длина тега в символах (1-10)

### Настройки отображения

- **Открывать ссылки в новой вкладке** - открывать теги в новом окне/вкладке
- **Показывать числовые теги** - отображать теги, начинающиеся с цифр
- **Показывать алфавитные теги** - отображать буквенные теги
- **Разделитель тегов** - символ между тегами
- **Пользовательский CSS** - дополнительные стили

## Интеграция с FiboSearch

Плагин автоматически определяет наличие FiboSearch и использует его AJAX поиск для более быстрой работы.

## Хуки для разработчиков

### Фильтры

```php
// Изменить первое слово из названия товара
add_filter('wc_product_tags_cloud_first_word', 'my_custom_first_word', 10, 2);

// Изменить ключ кэша
add_filter('wc_product_tags_cloud_cache_key', 'my_custom_cache_key');

// Изменить обработанные теги
add_filter('wc_product_tags_cloud_processed_tags', 'my_custom_processed_tags');

// Изменить HTML вывод
add_filter('wc_product_tags_cloud_output', 'my_custom_output', 10, 2);
```

### Действия

```php
// Выполнить действие после очистки кэша
add_action('wc_product_tags_cloud_cache_cleared', 'my_cache_cleared_action');
```

## Требования

- WordPress 5.0+
- WooCommerce 5.0+
- PHP 7.4+

## Безопасность

Плагин включает защиту от:
- SQL-инъекций (подготовленные запросы)
- CSRF атак (nonce проверки)
- XSS атак (экранирование вывода)

## Производительность

- Двухуровневое кэширование (object cache + transients)
- Оптимизированные SQL запросы
- Ограничение количества обрабатываемых товаров
- Автоматическая очистка кэша при изменении товаров

## Поддержка

Для получения поддержки или сообщения об ошибках создайте issue в репозитории проекта.

## Лицензия

GPL v2 или более поздняя версия.
