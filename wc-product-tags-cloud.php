<?php
/**
 * Plugin Name: WooCommerce Product Tags Cloud
 * Description: Генерирует облако тегов из названий товаров в категории WooCommerce
 * Version: 1.0
 * Author: Your Name
 */

if (!defined('ABSPATH')) {
    exit;
}

class WC_Product_Tags_Cloud {
    private $cache_key = 'wc_product_tags_cloud_';
    private $cache_time = 86400; // 24 часа

    public function __construct() {
        add_shortcode('wc_product_tags_cloud', array($this, 'generate_tags_cloud'));
        add_action('woocommerce_archive_description', array($this, 'add_tags_to_archive'));
        add_action('woocommerce_before_shop_loop', array($this, 'add_tags_to_search'));
        add_action('save_post_product', array($this, 'clear_cache'));
        add_action('deleted_post', array($this, 'clear_cache'));
    }

    private function get_first_word($title) {
        $parts = explode(' ', trim($title), 2);
        $first_word = $parts[0];
        return trim(preg_replace('/[^\p{L}\p{N}]/u', '', $first_word));
    }

    private function is_numeric_tag($tag) {
        return preg_match('/^[0-9]/', $tag);
    }

    private function get_cache_key() {
        $key = $this->cache_key;
        if (is_search()) {
            $key .= 'search_' . md5(get_search_query());
        } elseif (is_product_category()) {
            $cat = get_queried_object();
            $key .= 'cat_' . $cat->term_id;
        }
        return $key;
    }

    private function get_all_tags() {
        // Генерируем ключ кэша
        $cache_key = $this->get_cache_key();
        
        // Пробуем получить данные из кэша с помощью фильтрации
        if (false !== ($cached_tags = wp_cache_get($cache_key, 'wc_product_tags'))) {
            return $cached_tags;
        }
        
        // Проверяем временный кэш
        if (false !== ($cached_tags = get_transient($cache_key))) {
            // Сохраняем в объектном кэше для быстрого доступа
            wp_cache_set($cache_key, $cached_tags, 'wc_product_tags', 3600);
            return $cached_tags;
        }

        global $wpdb;
        $where = "post_type = 'product' AND post_status = 'publish'";
        
        if (is_search()) {
            $search_term = get_search_query();
            $where .= $wpdb->prepare(" AND post_title LIKE %s", '%' . $wpdb->esc_like($search_term) . '%');
        } elseif (is_product_category()) {
            $cat = get_queried_object();
            $where .= $wpdb->prepare(" AND ID IN (
                SELECT object_id FROM {$wpdb->term_relationships}
                WHERE term_taxonomy_id = %d
            )", $cat->term_id);
        }

        // Оптимизированный SQL запрос с LIMIT
        $products = $wpdb->get_col($wpdb->prepare("
            SELECT DISTINCT post_title
            FROM {$wpdb->posts}
            WHERE {$where}
            GROUP BY post_title
            LIMIT 1000
        "));

        // Предварительное выделение памяти для массивов
        $numeric_tags = array();
        $alpha_tags = array();
        $processed = array();

        // Обработка тегов с предварительной фильтрацией
        foreach ($products as $title) {
            $main_word = $this->get_first_word($title);
            
            // Пропускаем уже обработанные и пустые теги
            if (empty($main_word) || strlen($main_word) <= 1 || isset($processed[$main_word])) {
                continue;
            }
            
            // Отмечаем тег как обработанный
            $processed[$main_word] = true;
            
            // Распределяем по массивам
            if ($this->is_numeric_tag($main_word)) {
                $numeric_tags[$main_word] = 1;
            } else {
                $alpha_tags[$main_word] = 1;
            }
        }

        // Очищаем неиспользуемые данные
        unset($processed);

        // Формируем результат
        $tags = array($numeric_tags, $alpha_tags);
        
        // Сохраняем в двух уровнях кэша
        set_transient($cache_key, $tags, $this->cache_time);
        wp_cache_set($cache_key, $tags, 'wc_product_tags', 3600);
        
        return $tags;
    }

    public function generate_tags_cloud($atts) {
        if (!class_exists('WooCommerce')) {
            return 'WooCommerce не установлен';
        }

        list($numeric_tags, $alpha_tags) = $this->get_all_tags();

        ksort($numeric_tags);
        ksort($alpha_tags);

        $output = '<div class="cloud" style="margin: 0 0 20px; text-align: center; line-height: 2; border-bottom: 1px solid gray; padding-bottom: 10px;">' . PHP_EOL;
        
        if (!empty($numeric_tags)) {
            $output .= '    <!-- Номера моделей авто -->' . PHP_EOL;
            $output .= '    ' . $this->format_tags(array_keys($numeric_tags)) . PHP_EOL;
        }
        
        if (!empty($alpha_tags)) {
            $output .= PHP_EOL . '    <!-- Алфавитные теги -->' . PHP_EOL;
            $output .= '    ' . $this->format_tags(array_keys($alpha_tags)) . PHP_EOL;
        }

        $output .= '  </div>';

        return $output;
    }

    private function format_tags($tags) {
        $formatted_tags = array();
        foreach ($tags as $tag) {
            // Проверяем наличие FiboSearch
            if (class_exists('DGWT_WC_Ajax_Search')) {
                $formatted_tags[] = sprintf(
                    '<a href="#" class="dgwt-wcas-tag-cloud" data-value="%s" style="display: inline; text-decoration: none; color: #a3892c; padding: 3px 5px; border-bottom: 1px dashed #a3892c;background:none;">%s</a>',
                    esc_attr($tag),
                    esc_html($tag)
                );
            } else {
                $formatted_tags[] = sprintf(
                    '<a href="%s" style="display: inline; text-decoration: none; color: #a3892c; padding: 3px 5px;">%s</a>',
                    esc_url(add_query_arg(array('s' => $tag, 'post_type' => 'product'), home_url())),
                    esc_html($tag)
                );
            }
        }
        return implode(' / ', $formatted_tags);
    }

    public function add_tags_to_archive() {
        if (is_product_category()) {
            echo do_shortcode('[wc_product_tags_cloud]');
        }
    }

    public function add_tags_to_search() {
        if (is_search()) {
            echo do_shortcode('[wc_product_tags_cloud]');
        }
    }

    public function clear_cache() {
        global $wpdb;
        $wpdb->query(
            $wpdb->prepare(
                "DELETE FROM {$wpdb->options} WHERE option_name LIKE %s",
                '_transient_' . $this->cache_key . '%'
            )
        );
    }
}

function init_wc_product_tags_cloud() {
    new WC_Product_Tags_Cloud();
}
add_action('plugins_loaded', 'init_wc_product_tags_cloud');

// Добавляем JavaScript для интеграции с FiboSearch
function wc_product_tags_cloud_scripts() {
    if (class_exists('DGWT_WC_Ajax_Search')) {
        ?>
        <script>
        jQuery(document).ready(function($) {
            $('.dgwt-wcas-tag-cloud').on('click', function(e) {
                e.preventDefault();
                var searchValue = $(this).data('value');
                
                // Просто переходим на страницу поиска с параметром dgwt_wcas=1
                window.location.href = '/?s=' + encodeURIComponent(searchValue) + '&post_type=product&dgwt_wcas=1';
            });
        });
        </script>
        <?php
    }
}
add_action('wp_footer', 'wc_product_tags_cloud_scripts');