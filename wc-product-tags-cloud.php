<?php
/**
 * Plugin Name: WooCommerce Product Tags Cloud
 * Description: Генерирует облако тегов из названий товаров в категории WooCommerce
 * Version: 1.0
 * Author: Your Name
 */

if (!defined('ABSPATH')) {
    exit;
}

class WC_Product_Tags_Cloud {
    private $cache_key = 'wc_product_tags_cloud_';
    private $cache_time = 86400; // 24 часа
    private $options;

    public function __construct() {
        $this->load_options();
        add_shortcode('wc_product_tags_cloud', array($this, 'generate_tags_cloud'));
        add_action('woocommerce_archive_description', array($this, 'add_tags_to_archive'));
        add_action('woocommerce_before_shop_loop', array($this, 'add_tags_to_search'));
        add_action('save_post_product', array($this, 'clear_cache'));
        add_action('deleted_post', array($this, 'clear_cache'));

        // Админ панель
        if (is_admin()) {
            add_action('admin_menu', array($this, 'add_admin_menu'));
            add_action('admin_init', array($this, 'admin_init'));
        }
    }

    private function load_options() {
        $defaults = array(
            'open_links_in_new_tab' => false,
            'max_products' => 1000,
            'min_tag_length' => 2,
            'sort_order' => 'alphabetical' // alphabetical, natural, none
        );
        $this->options = wp_parse_args(get_option('wc_product_tags_cloud_options', array()), $defaults);
    }

    /**
     * Сортировка тегов с учетом русского алфавита
     */
    private function sort_tags_alphabetically($tags) {
        if (empty($tags)) {
            return $tags;
        }

        $keys = array_keys($tags);

        // Используем простую сортировку с учетом русского алфавита
        usort($keys, array($this, 'compare_russian_strings'));

        // Пересоздаем массив в правильном порядке
        $sorted_tags = array();
        foreach ($keys as $key) {
            $sorted_tags[$key] = $tags[$key];
        }

        return $sorted_tags;
    }

    /**
     * Сравнение строк с учетом русского алфавита
     */
    private function compare_russian_strings($a, $b) {
        // Приводим к нижнему регистру для сравнения
        $a_lower = mb_strtolower($a, 'UTF-8');
        $b_lower = mb_strtolower($b, 'UTF-8');

        // Создаем массив для правильного порядка русских букв
        $russian_alphabet = array(
            'а' => 1, 'б' => 2, 'в' => 3, 'г' => 4, 'д' => 5, 'е' => 6, 'ё' => 7, 'ж' => 8, 'з' => 9,
            'и' => 10, 'й' => 11, 'к' => 12, 'л' => 13, 'м' => 14, 'н' => 15, 'о' => 16, 'п' => 17,
            'р' => 18, 'с' => 19, 'т' => 20, 'у' => 21, 'ф' => 22, 'х' => 23, 'ц' => 24, 'ч' => 25,
            'ш' => 26, 'щ' => 27, 'ъ' => 28, 'ы' => 29, 'ь' => 30, 'э' => 31, 'ю' => 32, 'я' => 33
        );

        // Получаем первые символы
        $a_first = mb_substr($a_lower, 0, 1, 'UTF-8');
        $b_first = mb_substr($b_lower, 0, 1, 'UTF-8');

        // Проверяем, есть ли символы в русском алфавите
        $a_is_russian = isset($russian_alphabet[$a_first]);
        $b_is_russian = isset($russian_alphabet[$b_first]);

        // Если оба русские - сравниваем по позиции в алфавите
        if ($a_is_russian && $b_is_russian) {
            $a_pos = $russian_alphabet[$a_first];
            $b_pos = $russian_alphabet[$b_first];

            if ($a_pos !== $b_pos) {
                return $a_pos - $b_pos;
            }
            // Если первые буквы одинаковые, сравниваем строки целиком
            return strcmp($a_lower, $b_lower);
        }

        // Если один русский, другой нет - русские идут после
        if ($a_is_russian && !$b_is_russian) {
            return 1;
        }
        if (!$a_is_russian && $b_is_russian) {
            return -1;
        }

        // Если оба не русские - обычное сравнение
        return strcmp($a_lower, $b_lower);
    }

    private function get_first_word($title) {
        $parts = explode(' ', trim($title), 2);
        $first_word = $parts[0];
        return trim(preg_replace('/[^\p{L}\p{N}]/u', '', $first_word));
    }

    private function is_numeric_tag($tag) {
        return preg_match('/^[0-9]/', $tag);
    }

    private function get_cache_key() {
        $key = $this->cache_key;
        if (is_search()) {
            $search_query = sanitize_text_field(get_search_query());
            $key .= 'search_' . md5($search_query);
        } elseif (is_product_category()) {
            $cat = get_queried_object();
            if ($cat && isset($cat->term_id)) {
                $key .= 'cat_' . intval($cat->term_id);
            }
        } else {
            $key .= 'general';
        }
        return $key;
    }

    private function get_all_tags() {
        try {
            $cache_key = $this->get_cache_key();

            // Пробуем получить данные из кэша
            if (false !== ($cached_tags = wp_cache_get($cache_key, 'wc_product_tags'))) {
                return $cached_tags;
            }

            // Проверяем временный кэш
            if (false !== ($cached_tags = get_transient($cache_key))) {
                wp_cache_set($cache_key, $cached_tags, 'wc_product_tags', 3600);
                return $cached_tags;
            }

            global $wpdb;

            // Строим безопасный SQL запрос
            $sql = "SELECT DISTINCT post_title FROM {$wpdb->posts} WHERE post_type = 'product' AND post_status = 'publish'";
            $sql_params = array();

            if (is_search()) {
                $search_term = sanitize_text_field(get_search_query());
                if (!empty($search_term)) {
                    $sql .= " AND post_title LIKE %s";
                    $sql_params[] = '%' . $wpdb->esc_like($search_term) . '%';
                }
            } elseif (is_product_category()) {
                $cat = get_queried_object();
                if ($cat && isset($cat->term_id)) {
                    // Оптимизированный JOIN вместо подзапроса для ускорения
                    $sql .= " AND EXISTS (
                        SELECT 1 FROM {$wpdb->term_relationships} tr
                        INNER JOIN {$wpdb->term_taxonomy} tt ON tr.term_taxonomy_id = tt.term_taxonomy_id
                        WHERE tr.object_id = {$wpdb->posts}.ID
                        AND tt.term_id = %d
                        AND tt.taxonomy = 'product_cat'
                    )";
                    $sql_params[] = intval($cat->term_id);
                }
            }

            // Добавляем лимит
            $max_products = intval($this->options['max_products']);
            $sql .= " ORDER BY post_title LIMIT %d";
            $sql_params[] = $max_products;

            // Выполняем запрос безопасно
            if (!empty($sql_params)) {
                $products = $wpdb->get_col($wpdb->prepare($sql, $sql_params));
            } else {
                $products = $wpdb->get_col($sql);
            }

            if ($wpdb->last_error) {
                error_log('WC Product Tags Cloud SQL Error: ' . $wpdb->last_error);
                return array(array(), array());
            }

            // Обработка тегов
            $numeric_tags = array();
            $alpha_tags = array();
            $processed = array();
            $min_length = intval($this->options['min_tag_length']);

            foreach ($products as $title) {
                $main_word = $this->get_first_word($title);

                // Пропускаем короткие, пустые или уже обработанные теги
                if (empty($main_word) || strlen($main_word) < $min_length || isset($processed[$main_word])) {
                    continue;
                }

                $processed[$main_word] = true;

                if ($this->is_numeric_tag($main_word)) {
                    $numeric_tags[$main_word] = 1;
                } else {
                    $alpha_tags[$main_word] = 1;
                }
            }

            // Сортируем теги в зависимости от настроек
            ksort($numeric_tags, SORT_NATURAL);

            $sort_order = $this->options['sort_order'];
            if ($sort_order === 'alphabetical') {
                $alpha_tags = $this->sort_tags_alphabetically($alpha_tags);
            } elseif ($sort_order === 'natural') {
                ksort($alpha_tags, SORT_NATURAL);
            }
            // Если 'none', то не сортируем

            $tags = array($numeric_tags, $alpha_tags);

            // Сохраняем в кэше
            set_transient($cache_key, $tags, $this->cache_time);
            wp_cache_set($cache_key, $tags, 'wc_product_tags', 3600);

            return $tags;

        } catch (Exception $e) {
            error_log('WC Product Tags Cloud Error: ' . $e->getMessage());
            return array(array(), array());
        }
    }

    public function generate_tags_cloud($atts) {
        if (!class_exists('WooCommerce')) {
            return '<div style="color: red;">WooCommerce не установлен</div>';
        }

        try {
            list($numeric_tags, $alpha_tags) = $this->get_all_tags();

            if (empty($numeric_tags) && empty($alpha_tags)) {
                return '<div class="cloud" style="margin: 0 0 20px; text-align: center; color: #666; font-style: italic;">Теги не найдены</div>';
            }

            $output = '<div class="cloud" style="margin: 0 0 20px; text-align: center; line-height: 2; border-bottom: 1px solid gray; padding-bottom: 10px;">' . PHP_EOL;

            if (!empty($numeric_tags)) {
                $output .= '    <!-- Номера моделей авто -->' . PHP_EOL;
                $output .= '    ' . $this->format_tags(array_keys($numeric_tags)) . PHP_EOL;
            }

            if (!empty($alpha_tags)) {
                $output .= PHP_EOL . '    <!-- Алфавитные теги -->' . PHP_EOL;
                $output .= '    ' . $this->format_tags(array_keys($alpha_tags)) . PHP_EOL;
            }

            $output .= '</div>';

            return $output;

        } catch (Exception $e) {
            error_log('WC Product Tags Cloud Error in generate_tags_cloud: ' . $e->getMessage());
            return '<div style="color: red;">Ошибка генерации облака тегов</div>';
        }
    }

    private function format_tags($tags) {
        if (empty($tags)) {
            return '';
        }

        $formatted_tags = array();
        $target = $this->options['open_links_in_new_tab'] ? ' target="_blank" rel="noopener noreferrer"' : '';

        foreach ($tags as $tag) {
            $tag = sanitize_text_field($tag);

            // Проверяем наличие FiboSearch
            if (class_exists('DGWT_WC_Ajax_Search')) {
                $formatted_tags[] = sprintf(
                    '<a href="#" class="dgwt-wcas-tag-cloud" data-value="%s" style="display: inline; text-decoration: none; color: #a3892c; padding: 3px 5px; border-bottom: 1px dashed #a3892c; background: none;"%s>%s</a>',
                    esc_attr($tag),
                    $target,
                    esc_html($tag)
                );
            } else {
                $search_url = add_query_arg(array(
                    's' => $tag,
                    'post_type' => 'product'
                ), home_url('/'));

                $formatted_tags[] = sprintf(
                    '<a href="%s" style="display: inline; text-decoration: none; color: #a3892c; padding: 3px 5px;"%s>%s</a>',
                    esc_url($search_url),
                    $target,
                    esc_html($tag)
                );
            }
        }

        return implode(' / ', $formatted_tags);
    }

    public function add_tags_to_archive() {
        if (is_product_category()) {
            echo wp_kses_post(do_shortcode('[wc_product_tags_cloud]'));
        }
    }

    public function add_tags_to_search() {
        if (is_search() && get_query_var('post_type') === 'product') {
            echo wp_kses_post(do_shortcode('[wc_product_tags_cloud]'));
        }
    }

    public function clear_cache($post_id = null) {
        try {
            global $wpdb;

            // Очищаем transients и их timeout записи
            $wpdb->query($wpdb->prepare(
                "DELETE FROM {$wpdb->options} WHERE option_name LIKE %s OR option_name LIKE %s",
                '_transient_' . $this->cache_key . '%',
                '_transient_timeout_' . $this->cache_key . '%'
            ));

            // Очищаем object cache
            wp_cache_flush_group('wc_product_tags');

            return true;

        } catch (Exception $e) {
            error_log('WC Product Tags Cloud Error clearing cache: ' . $e->getMessage());
            return false;
        }
    }

    // Админ панель
    public function add_admin_menu() {
        add_options_page(
            'WC Product Tags Cloud Settings',
            'Product Tags Cloud',
            'manage_options',
            'wc-product-tags-cloud',
            array($this, 'admin_page')
        );
    }

    public function admin_init() {
        register_setting('wc_product_tags_cloud_options', 'wc_product_tags_cloud_options', array($this, 'validate_options'));

        add_settings_section('wc_product_tags_cloud_main', 'Основные настройки', null, 'wc-product-tags-cloud');

        add_settings_field('open_links_in_new_tab', 'Открывать ссылки в новой вкладке', array($this, 'open_links_callback'), 'wc-product-tags-cloud', 'wc_product_tags_cloud_main');
        add_settings_field('max_products', 'Максимум товаров для обработки', array($this, 'max_products_callback'), 'wc-product-tags-cloud', 'wc_product_tags_cloud_main');
        add_settings_field('min_tag_length', 'Минимальная длина тега', array($this, 'min_tag_length_callback'), 'wc-product-tags-cloud', 'wc_product_tags_cloud_main');
        add_settings_field('sort_order', 'Порядок сортировки тегов', array($this, 'sort_order_callback'), 'wc-product-tags-cloud', 'wc_product_tags_cloud_main');
    }

    public function open_links_callback() {
        printf('<input type="checkbox" id="open_links_in_new_tab" name="wc_product_tags_cloud_options[open_links_in_new_tab]" value="1" %s />', checked(1, $this->options['open_links_in_new_tab'], false));
        echo '<label for="open_links_in_new_tab"> Открывать теги в новой вкладке</label>';
    }

    public function max_products_callback() {
        printf('<input type="number" id="max_products" name="wc_product_tags_cloud_options[max_products]" value="%s" min="100" max="5000" />', esc_attr($this->options['max_products']));
        echo '<p class="description">Максимальное количество товаров для обработки (100-5000). По умолчанию: 1000</p>';
    }

    public function min_tag_length_callback() {
        printf('<input type="number" id="min_tag_length" name="wc_product_tags_cloud_options[min_tag_length]" value="%s" min="1" max="10" />', esc_attr($this->options['min_tag_length']));
        echo '<p class="description">Минимальная длина тега в символах (1-10). По умолчанию: 2</p>';
    }

    public function sort_order_callback() {
        $current = $this->options['sort_order'];
        ?>
        <select id="sort_order" name="wc_product_tags_cloud_options[sort_order]">
            <option value="alphabetical" <?php selected($current, 'alphabetical'); ?>>По алфавиту (А-Я)</option>
            <option value="natural" <?php selected($current, 'natural'); ?>>Естественная сортировка</option>
            <option value="none" <?php selected($current, 'none'); ?>>Без сортировки</option>
        </select>
        <p class="description">Выберите порядок сортировки алфавитных тегов. "По алфавиту" учитывает русский алфавит.</p>
        <?php
    }

    public function admin_page() {
        if (!current_user_can('manage_options')) {
            return;
        }

        // Обработка очистки кэша
        if (isset($_POST['clear_cache']) && wp_verify_nonce($_POST['_wpnonce'], 'wc_product_tags_cloud_clear_cache')) {
            if ($this->clear_cache()) {
                echo '<div class="notice notice-success"><p>Кэш успешно очищен!</p></div>';
            } else {
                echo '<div class="notice notice-error"><p>Ошибка при очистке кэша.</p></div>';
            }
        }

        ?>
        <div class="wrap">
            <h1>Настройки WC Product Tags Cloud</h1>

            <form action="options.php" method="post">
                <?php
                settings_fields('wc_product_tags_cloud_options');
                do_settings_sections('wc-product-tags-cloud');
                submit_button('Сохранить настройки');
                ?>
            </form>

            <hr>

            <h2>Управление кэшем</h2>
            <form method="post" action="">
                <?php wp_nonce_field('wc_product_tags_cloud_clear_cache'); ?>
                <p>Очистить кэш плагина для принудительного обновления тегов.</p>
                <input type="submit" name="clear_cache" class="button button-secondary" value="Очистить кэш">
            </form>

            <hr>

            <h2>Использование</h2>
            <p>Используйте шорткод для отображения облака тегов:</p>
            <code>[wc_product_tags_cloud]</code>
        </div>
        <?php
    }

    public function validate_options($input) {
        $validated = array();

        $validated['open_links_in_new_tab'] = isset($input['open_links_in_new_tab']) ? 1 : 0;
        $validated['max_products'] = isset($input['max_products']) ? max(100, min(5000, intval($input['max_products']))) : 1000;
        $validated['min_tag_length'] = isset($input['min_tag_length']) ? max(1, min(10, intval($input['min_tag_length']))) : 2;

        // Валидация порядка сортировки
        $allowed_sorts = array('alphabetical', 'natural', 'none');
        $validated['sort_order'] = isset($input['sort_order']) && in_array($input['sort_order'], $allowed_sorts) ?
            $input['sort_order'] : 'alphabetical';

        // Очищаем кэш при изменении настроек
        $this->clear_cache();

        return $validated;
    }
}

function init_wc_product_tags_cloud() {
    new WC_Product_Tags_Cloud();
}
add_action('plugins_loaded', 'init_wc_product_tags_cloud');

// Добавляем JavaScript для интеграции с FiboSearch
function wc_product_tags_cloud_scripts() {
    if (class_exists('DGWT_WC_Ajax_Search')) {
        ?>
        <script>
        jQuery(document).ready(function($) {
            $('.dgwt-wcas-tag-cloud').on('click', function(e) {
                e.preventDefault();
                var searchValue = $(this).data('value');
                var url = '/?s=' + encodeURIComponent(searchValue) + '&post_type=product&dgwt_wcas=1';

                // Проверяем, нужно ли открывать в новой вкладке
                if ($(this).attr('target') === '_blank') {
                    window.open(url, '_blank', 'noopener,noreferrer');
                } else {
                    window.location.href = url;
                }
            });
        });
        </script>
        <?php
    }
}
add_action('wp_footer', 'wc_product_tags_cloud_scripts');

// Добавляем хуки активации/деактивации
register_activation_hook(__FILE__, function() {
    $default_options = array(
        'open_links_in_new_tab' => false,
        'max_products' => 1000,
        'min_tag_length' => 2,
        'sort_order' => 'alphabetical'
    );
    add_option('wc_product_tags_cloud_options', $default_options);
});

register_deactivation_hook(__FILE__, function() {
    // Очищаем кэш при деактивации
    global $wpdb;
    $wpdb->query("DELETE FROM {$wpdb->options} WHERE option_name LIKE '_transient_wc_product_tags_cloud_%' OR option_name LIKE '_transient_timeout_wc_product_tags_cloud_%'");
    wp_cache_flush_group('wc_product_tags');
});